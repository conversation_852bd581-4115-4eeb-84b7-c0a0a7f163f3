# YouTube Analytics Platform

A comprehensive YouTube analytics platform that allows users to track channel performance, analyze video statistics, and monitor trends over time. Built with a modern tech stack using React, Node.js, MongoDB, and Redis.

## Features

### Core Functionality
- **Channel Search & Management**: Search for YouTube channels and add them to your tracking list
- **Video Analytics**: Comprehensive video statistics including views, likes, comments, and duration
- **Time-based Analysis**: Track performance over different time periods (daily, weekly, monthly, yearly)
- **Channel Groups**: Group related channels together (e.g., Sidemen, Hermitcraft)
- **Queue System**: Background processing to respect YouTube API rate limits
- **Hashtag Analysis**: Track trending hashtags and their performance

### Analytics Features
- Upload frequency tracking
- Video duration analysis over time
- Views and engagement trends
- Comparative analytics between channels
- Top performing videos identification
- Subscriber growth tracking

### Technical Features
- **OOP Architecture**: Clean, maintainable code structure
- **Rate Limiting**: Intelligent API usage to stay within YouTube's quotas
- **Background Processing**: Queue-based system for data fetching
- **Real-time Updates**: Live queue status and job monitoring
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** for data storage
- **Redis** for queue management
- **Bull** for job processing
- **YouTube Data API v3** for data fetching
- **Mongoose** for MongoDB ODM

### Frontend
- **React** with Vite
- **React Router** for navigation
- **Chart.js** for data visualization
- **Axios** for API communication
- **CSS3** for styling (no frameworks)

## Project Structure

```
statyt/
├── backend/
│   ├── src/
│   │   ├── config/
│   │   │   └── database.js
│   │   ├── models/
│   │   │   ├── Channel.js
│   │   │   ├── Video.js
│   │   │   ├── ChannelGroup.js
│   │   │   └── QueueJob.js
│   │   ├── services/
│   │   │   ├── YouTubeAPI.js
│   │   │   ├── ChannelService.js
│   │   │   ├── VideoService.js
│   │   │   └── QueueManager.js
│   │   └── routes/
│   │       ├── channelRoutes.js
│   │       ├── analyticsRoutes.js
│   │       └── queueRoutes.js
│   ├── .env.example
│   ├── package.json
│   └── index.js
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   └── Header.jsx
│   │   ├── pages/
│   │   │   ├── Dashboard.jsx
│   │   │   ├── ChannelSearch.jsx
│   │   │   ├── ChannelDetails.jsx
│   │   │   ├── Analytics.jsx
│   │   │   └── QueueStatus.jsx
│   │   ├── services/
│   │   │   └── api.js
│   │   ├── App.jsx
│   │   └── main.jsx
│   ├── .env.example
│   └── package.json
└── README.md
```

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- Redis server
- YouTube Data API v3 key

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables in `.env`:
   ```
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/youtube-analytics
   YOUTUBE_API_KEY=your_youtube_api_key_here
   REDIS_URL=redis://localhost:6379
   NODE_ENV=development
   ```

5. Start the backend server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create environment file:
   ```bash
   cp .env.example .env
   ```

4. Configure the API URL in `.env`:
   ```
   VITE_API_URL=http://localhost:5000/api
   ```

5. Start the frontend development server:
   ```bash
   npm run dev
   ```

### Getting YouTube API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3
4. Create credentials (API key)
5. Restrict the API key to YouTube Data API v3 for security

## Usage

### Adding Channels
1. Navigate to "Search Channels" in the header
2. Search for YouTube channels by name or handle
3. Click "Add Channel" to start tracking

### Viewing Analytics
1. Go to the Dashboard to see overview statistics
2. Click on individual channels for detailed analytics
3. Use the Analytics page for comparative analysis

### Managing Queue
1. Visit the Queue Status page to monitor background jobs
2. Retry failed jobs if needed
3. Monitor API quota usage

## API Endpoints

### Channels
- `GET /api/channels/search?q={query}` - Search for channels
- `GET /api/channels` - Get all tracked channels
- `GET /api/channels/{channelId}` - Get specific channel
- `POST /api/channels/fetch` - Add channel to fetch queue
- `POST /api/channels/{channelId}/videos/fetch` - Fetch channel videos

### Analytics
- `GET /api/analytics/videos/{channelId}` - Get video analytics
- `POST /api/analytics/compare` - Compare multiple channels
- `GET /api/analytics/trending` - Get trending videos
- `GET /api/analytics/hashtags` - Get hashtag analytics

### Queue
- `GET /api/queue/stats` - Get queue statistics
- `GET /api/queue/jobs` - Get job history
- `POST /api/queue/jobs/{jobId}/retry` - Retry failed job

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Roadmap

- [ ] Advanced chart visualizations
- [ ] Channel group analytics
- [ ] Export functionality (CSV, PDF)
- [ ] Email notifications for milestones
- [ ] Advanced filtering and search
- [ ] User authentication and multi-user support
- [ ] API rate limiting optimization
- [ ] Real-time data updates via WebSockets
