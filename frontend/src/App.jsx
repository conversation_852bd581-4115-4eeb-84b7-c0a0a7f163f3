import { useState } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./components/Header";
import Dashboard from "./pages/Dashboard";
import ChannelSearch from "./pages/ChannelSearch";
import ChannelDetails from "./pages/ChannelDetails";
import Analytics from "./pages/Analytics";
import QueueStatus from "./pages/QueueStatus";
import "./App.css";

function App() {
  return (
    <Router>
      <div className="App">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/search" element={<ChannelSearch />} />
            <Route path="/channel/:channelId" element={<ChannelDetails />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/queue" element={<QueueStatus />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
