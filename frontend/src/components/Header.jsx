import { Link, useLocation } from 'react-router-dom';
import './Header.css';

function Header() {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <Link to="/">
            <h1>YouTube Analytics</h1>
          </Link>
        </div>
        
        <nav className="nav">
          <ul className="nav-list">
            <li>
              <Link to="/" className={isActive('/')}>
                Dashboard
              </Link>
            </li>
            <li>
              <Link to="/search" className={isActive('/search')}>
                Search Channels
              </Link>
            </li>
            <li>
              <Link to="/analytics" className={isActive('/analytics')}>
                Analytics
              </Link>
            </li>
            <li>
              <Link to="/queue" className={isActive('/queue')}>
                Queue Status
              </Link>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
}

export default Header;
