import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class APIService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Channel methods
  async searchChannels(query) {
    const response = await this.client.get('/channels/search', {
      params: { q: query }
    });
    return response.data;
  }

  async getAllChannels(page = 1, limit = 20, sortBy = 'lastFetched', sortOrder = 'desc') {
    const response = await this.client.get('/channels', {
      params: { page, limit, sortBy, sortOrder }
    });
    return response.data;
  }

  async getChannel(channelId) {
    const response = await this.client.get(`/channels/${channelId}`);
    return response.data;
  }

  async fetchChannel(channelId, channelName = null, priority = 0) {
    const response = await this.client.post('/channels/fetch', {
      channelId,
      channelName,
      priority
    });
    return response.data;
  }

  async fetchChannelVideos(channelId, limit = null, priority = 0) {
    const response = await this.client.post(`/channels/${channelId}/videos/fetch`, {
      limit,
      priority
    });
    return response.data;
  }

  async getChannelAnalytics(channelId, timeframe = 'all') {
    const response = await this.client.get(`/channels/${channelId}/analytics`, {
      params: { timeframe }
    });
    return response.data;
  }

  async updateChannelAnalytics(channelId, priority = 0) {
    const response = await this.client.post(`/channels/${channelId}/analytics/update`, {
      priority
    });
    return response.data;
  }

  async refreshChannel(channelId) {
    const response = await this.client.post(`/channels/${channelId}/refresh`);
    return response.data;
  }

  async deleteChannel(channelId) {
    const response = await this.client.delete(`/channels/${channelId}`);
    return response.data;
  }

  // Analytics methods
  async getVideoAnalytics(channelId, timeframe = 'all', groupBy = 'month') {
    const response = await this.client.get(`/analytics/videos/${channelId}`, {
      params: { timeframe, groupBy }
    });
    return response.data;
  }

  async getChannelVideos(channelId, page = 1, limit = 20, sortBy = 'publishedAt', sortOrder = 'desc') {
    const response = await this.client.get(`/analytics/videos/${channelId}/list`, {
      params: { page, limit, sortBy, sortOrder }
    });
    return response.data;
  }

  async compareChannels(channelIds, timeframe = 'all') {
    const response = await this.client.post('/analytics/compare', {
      channelIds,
      timeframe
    });
    return response.data;
  }

  async getTrendingVideos(timeframe = 'week', limit = 20, sortBy = 'viewCount') {
    const response = await this.client.get('/analytics/trending', {
      params: { timeframe, limit, sortBy }
    });
    return response.data;
  }

  async getHashtagAnalytics(channelId = null, timeframe = 'all', limit = 50) {
    const response = await this.client.get('/analytics/hashtags', {
      params: { channelId, timeframe, limit }
    });
    return response.data;
  }

  async getUploadFrequency(channelId, groupBy = 'month') {
    const response = await this.client.get(`/analytics/upload-frequency/${channelId}`, {
      params: { groupBy }
    });
    return response.data;
  }

  async getDurationAnalytics(channelId, timeframe = 'all') {
    const response = await this.client.get(`/analytics/duration/${channelId}`, {
      params: { timeframe }
    });
    return response.data;
  }

  // Queue methods
  async getQueueStats() {
    const response = await this.client.get('/queue/stats');
    return response.data;
  }

  async getQueueJobs(page = 1, limit = 20, status = null, type = null, sortBy = 'createdAt', sortOrder = 'desc') {
    const response = await this.client.get('/queue/jobs', {
      params: { page, limit, status, type, sortBy, sortOrder }
    });
    return response.data;
  }

  async getJob(jobId) {
    const response = await this.client.get(`/queue/jobs/${jobId}`);
    return response.data;
  }

  async retryJob(jobId) {
    const response = await this.client.post(`/queue/jobs/${jobId}/retry`);
    return response.data;
  }

  async pauseQueue(queueName) {
    const response = await this.client.post(`/queue/pause/${queueName}`);
    return response.data;
  }

  async resumeQueue(queueName) {
    const response = await this.client.post(`/queue/resume/${queueName}`);
    return response.data;
  }

  async clearCompletedJobs(olderThan = 7) {
    const response = await this.client.delete('/queue/jobs/completed', {
      params: { olderThan }
    });
    return response.data;
  }

  async getQueueHealth() {
    const response = await this.client.get('/queue/health');
    return response.data;
  }

  // Utility methods
  async healthCheck() {
    const response = await this.client.get('/health');
    return response.data;
  }

  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
  }

  formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
  }
}

export default new APIService();
