.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.dashboard-header p {
  font-size: 1.1rem;
  color: #666;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stats-card,
.queue-card,
.channels-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.channels-card {
  grid-column: 1 / -1;
}

.stats-card h2,
.queue-card h2,
.channels-card h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.queue-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.queue-item {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.queue-item h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: #333;
}

.queue-numbers {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.queue-stat {
  font-size: 0.85rem;
  color: #666;
}

.view-queue-btn {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.view-queue-btn:hover {
  background: #5a67d8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.add-channel-btn {
  padding: 0.5rem 1rem;
  background: #10b981;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.add-channel-btn:hover {
  background: #059669;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.empty-state p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.channels-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.channel-item:hover {
  background: #f1f5f9;
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.channel-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.channel-details h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.channel-details h3 a {
  color: #333;
  text-decoration: none;
}

.channel-details h3 a:hover {
  color: #667eea;
}

.channel-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.channel-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

.status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.7rem;
}

.status.completed {
  background: #d1fae5;
  color: #065f46;
}

.status.pending {
  background: #fef3c7;
  color: #92400e;
}

.status.fetching {
  background: #dbeafe;
  color: #1e40af;
}

.status.error {
  background: #fee2e2;
  color: #991b1b;
}

.channel-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;
}

.btn-small {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
}

.btn:not(.btn-primary) {
  background: #e5e7eb;
  color: #374151;
}

.btn:not(.btn-primary):hover {
  background: #d1d5db;
}

.card-footer {
  margin-top: 1rem;
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.view-all-btn {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.view-all-btn:hover {
  text-decoration: underline;
}

.loading,
.error {
  text-align: center;
  padding: 3rem;
  font-size: 1.1rem;
}

.error {
  color: #dc2626;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .channel-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .channel-actions {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .channel-stats {
    flex-direction: column;
    gap: 0.25rem;
  }
}
