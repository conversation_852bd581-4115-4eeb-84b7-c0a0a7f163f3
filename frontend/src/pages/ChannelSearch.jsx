import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';
import './ChannelSearch.css';

function ChannelSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [addingChannels, setAddingChannels] = useState(new Set());
  const navigate = useNavigate();

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      setError(null);
      const data = await api.searchChannels(searchQuery.trim());
      setSearchResults(data.channels);
    } catch (err) {
      setError(err.message);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddChannel = async (channel) => {
    try {
      setAddingChannels(prev => new Set(prev).add(channel.channelId));
      
      // Add channel to fetch queue
      await api.fetchChannel(channel.channelId, channel.channelName, 1);
      
      // Navigate to channel details page
      navigate(`/channel/${channel.channelId}`);
    } catch (err) {
      console.error('Failed to add channel:', err);
      alert('Failed to add channel. Please try again.');
    } finally {
      setAddingChannels(prev => {
        const newSet = new Set(prev);
        newSet.delete(channel.channelId);
        return newSet;
      });
    }
  };

  return (
    <div className="channel-search">
      <div className="search-header">
        <h1>Search YouTube Channels</h1>
        <p>Find and add YouTube channels to track their analytics</p>
      </div>

      <form onSubmit={handleSearch} className="search-form">
        <div className="search-input-group">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Enter channel name, handle, or keywords..."
            className="search-input"
          />
          <button 
            type="submit" 
            disabled={loading || !searchQuery.trim()}
            className="search-button"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </form>

      {error && (
        <div className="error-message">
          <p>Error: {error}</p>
        </div>
      )}

      {searchResults.length > 0 && (
        <div className="search-results">
          <h2>Search Results</h2>
          <div className="results-grid">
            {searchResults.map((channel) => (
              <div key={channel.channelId} className="channel-card">
                <div className="channel-header">
                  <img 
                    src={channel.thumbnailUrl} 
                    alt={channel.channelName}
                    className="channel-avatar"
                  />
                  <div className="channel-info">
                    <h3>{channel.channelName}</h3>
                    <p className="channel-description">
                      {channel.description 
                        ? channel.description.substring(0, 150) + (channel.description.length > 150 ? '...' : '')
                        : 'No description available'
                      }
                    </p>
                    <div className="channel-meta">
                      <span>Published: {api.formatDate(channel.publishedAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="channel-actions">
                  <button
                    onClick={() => handleAddChannel(channel)}
                    disabled={addingChannels.has(channel.channelId)}
                    className="add-button"
                  >
                    {addingChannels.has(channel.channelId) ? 'Adding...' : 'Add Channel'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!loading && !error && searchResults.length === 0 && searchQuery && (
        <div className="no-results">
          <p>No channels found for "{searchQuery}". Try different keywords.</p>
        </div>
      )}

      <div className="search-tips">
        <h3>Search Tips</h3>
        <ul>
          <li>Use specific channel names for better results</li>
          <li>Try searching with channel handles (e.g., @channelname)</li>
          <li>Use keywords related to the channel's content</li>
          <li>Check spelling and try variations of the name</li>
        </ul>
      </div>
    </div>
  );
}

export default ChannelSearch;
