import { useState, useEffect } from 'react';
import api from '../services/api';

function Analytics() {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadChannels();
  }, []);

  const loadChannels = async () => {
    try {
      const data = await api.getAllChannels(1, 50);
      setChannels(data.channels);
    } catch (err) {
      console.error('Failed to load channels:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading analytics...</div>;

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Analytics Overview</h1>
      
      <div style={{ background: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '2rem' }}>
        <h2>All Channels</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
          {channels.map((channel) => (
            <div key={channel.channelId} style={{ 
              border: '1px solid #e5e7eb', 
              padding: '1rem', 
              borderRadius: '8px',
              background: '#f8fafc'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
                <img 
                  src={channel.thumbnailUrl} 
                  alt={channel.channelName}
                  style={{ width: '40px', height: '40px', borderRadius: '50%' }}
                />
                <h3 style={{ margin: 0, fontSize: '1rem' }}>{channel.channelName}</h3>
              </div>
              
              <div style={{ fontSize: '0.9rem', color: '#666' }}>
                <p>Subscribers: {api.formatNumber(channel.subscriberCount)}</p>
                <p>Videos: {channel.analytics?.totalVideos || 0}</p>
                <p>Total Views: {api.formatNumber(channel.analytics?.totalViews || 0)}</p>
                <p>Avg Views: {api.formatNumber(channel.analytics?.averageViews || 0)}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{ background: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h2>Summary Statistics</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div style={{ textAlign: 'center', padding: '1rem', background: '#f8fafc', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#667eea' }}>
              {channels.length}
            </h3>
            <p style={{ margin: 0, color: '#666' }}>Total Channels</p>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem', background: '#f8fafc', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#667eea' }}>
              {api.formatNumber(channels.reduce((sum, ch) => sum + (ch.subscriberCount || 0), 0))}
            </h3>
            <p style={{ margin: 0, color: '#666' }}>Total Subscribers</p>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem', background: '#f8fafc', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#667eea' }}>
              {channels.reduce((sum, ch) => sum + (ch.analytics?.totalVideos || 0), 0)}
            </h3>
            <p style={{ margin: 0, color: '#666' }}>Total Videos</p>
          </div>
          <div style={{ textAlign: 'center', padding: '1rem', background: '#f8fafc', borderRadius: '8px' }}>
            <h3 style={{ margin: '0 0 0.5rem 0', color: '#667eea' }}>
              {api.formatNumber(channels.reduce((sum, ch) => sum + (ch.analytics?.totalViews || 0), 0))}
            </h3>
            <p style={{ margin: 0, color: '#666' }}>Total Views</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Analytics;
