import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import api from '../services/api';
import './Dashboard.css';

function Dashboard() {
  const [channels, setChannels] = useState([]);
  const [queueStats, setQueueStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [channelsData, queueData] = await Promise.all([
        api.getAllChannels(1, 10, 'lastFetched', 'desc'),
        api.getQueueStats()
      ]);
      
      setChannels(channelsData.channels);
      setQueueStats(queueData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshChannel = async (channelId) => {
    try {
      await api.fetchChannel(channelId, null, 1);
      // Refresh the dashboard data
      loadDashboardData();
    } catch (err) {
      console.error('Failed to refresh channel:', err);
    }
  };

  if (loading) {
    return (
      <div className="dashboard">
        <div className="loading">Loading dashboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard">
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>YouTube Analytics Dashboard</h1>
        <p>Monitor your YouTube channels and track their performance</p>
      </div>

      <div className="dashboard-grid">
        {/* Quick Stats */}
        <div className="stats-card">
          <h2>Quick Stats</h2>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-number">{channels.length}</span>
              <span className="stat-label">Tracked Channels</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {channels.reduce((sum, ch) => sum + (ch.analytics?.totalVideos || 0), 0)}
              </span>
              <span className="stat-label">Total Videos</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {api.formatNumber(channels.reduce((sum, ch) => sum + (ch.analytics?.totalViews || 0), 0))}
              </span>
              <span className="stat-label">Total Views</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {api.formatNumber(channels.reduce((sum, ch) => sum + (ch.subscriberCount || 0), 0))}
              </span>
              <span className="stat-label">Total Subscribers</span>
            </div>
          </div>
        </div>

        {/* Queue Status */}
        {queueStats && (
          <div className="queue-card">
            <h2>Queue Status</h2>
            <div className="queue-stats">
              {Object.entries(queueStats.queueStats).map(([queueName, stats]) => (
                <div key={queueName} className="queue-item">
                  <h3>{queueName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h3>
                  <div className="queue-numbers">
                    <span className="queue-stat">
                      <strong>{stats.waiting}</strong> waiting
                    </span>
                    <span className="queue-stat">
                      <strong>{stats.active}</strong> active
                    </span>
                    <span className="queue-stat">
                      <strong>{stats.completed}</strong> completed
                    </span>
                    <span className="queue-stat">
                      <strong>{stats.failed}</strong> failed
                    </span>
                  </div>
                </div>
              ))}
            </div>
            <Link to="/queue" className="view-queue-btn">
              View Queue Details
            </Link>
          </div>
        )}

        {/* Recent Channels */}
        <div className="channels-card">
          <div className="card-header">
            <h2>Recent Channels</h2>
            <Link to="/search" className="add-channel-btn">
              Add Channel
            </Link>
          </div>
          
          {channels.length === 0 ? (
            <div className="empty-state">
              <p>No channels tracked yet.</p>
              <Link to="/search" className="btn btn-primary">
                Search for Channels
              </Link>
            </div>
          ) : (
            <div className="channels-list">
              {channels.map((channel) => (
                <div key={channel.channelId} className="channel-item">
                  <div className="channel-info">
                    <img 
                      src={channel.thumbnailUrl} 
                      alt={channel.channelName}
                      className="channel-thumbnail"
                    />
                    <div className="channel-details">
                      <h3>
                        <Link to={`/channel/${channel.channelId}`}>
                          {channel.channelName}
                        </Link>
                      </h3>
                      <div className="channel-stats">
                        <span>{api.formatNumber(channel.subscriberCount)} subscribers</span>
                        <span>{channel.analytics?.totalVideos || 0} videos</span>
                        <span>{api.formatNumber(channel.analytics?.totalViews || 0)} views</span>
                      </div>
                      <div className="channel-meta">
                        <span className={`status ${channel.fetchStatus}`}>
                          {channel.fetchStatus}
                        </span>
                        <span className="last-updated">
                          Updated: {api.formatDate(channel.lastFetched)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="channel-actions">
                    <button 
                      onClick={() => handleRefreshChannel(channel.channelId)}
                      className="btn btn-small"
                    >
                      Refresh
                    </button>
                    <Link 
                      to={`/channel/${channel.channelId}`}
                      className="btn btn-small btn-primary"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {channels.length > 0 && (
            <div className="card-footer">
              <Link to="/analytics" className="view-all-btn">
                View All Channels
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
