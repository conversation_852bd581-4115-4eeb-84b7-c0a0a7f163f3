.channel-search {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.search-header {
  text-align: center;
  margin-bottom: 2rem;
}

.search-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.search-header p {
  font-size: 1.1rem;
  color: #666;
}

.search-form {
  margin-bottom: 2rem;
}

.search-input-group {
  display: flex;
  max-width: 600px;
  margin: 0 auto;
  gap: 0.5rem;
}

.search-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-button {
  padding: 1rem 2rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s ease;
  white-space: nowrap;
}

.search-button:hover:not(:disabled) {
  background: #5a67d8;
}

.search-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.error-message {
  background: #fee2e2;
  border: 1px solid #fecaca;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
}

.search-results {
  margin-bottom: 3rem;
}

.search-results h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.results-grid {
  display: grid;
  gap: 1.5rem;
}

.channel-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.channel-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.channel-header {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.channel-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.channel-info {
  flex: 1;
}

.channel-info h3 {
  font-size: 1.3rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.channel-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.channel-meta {
  font-size: 0.9rem;
  color: #888;
}

.channel-actions {
  display: flex;
  justify-content: flex-end;
}

.add-button {
  padding: 0.75rem 1.5rem;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.add-button:hover:not(:disabled) {
  background: #059669;
}

.add-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: #666;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.search-tips {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
}

.search-tips h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.search-tips ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.search-tips li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .channel-search {
    padding: 1rem;
  }
  
  .search-input-group {
    flex-direction: column;
  }
  
  .search-button {
    padding: 1rem;
  }
  
  .channel-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .channel-avatar {
    width: 100px;
    height: 100px;
  }
  
  .channel-actions {
    justify-content: center;
  }
}
