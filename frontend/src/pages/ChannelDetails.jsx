import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import api from '../services/api';

function ChannelDetails() {
  const { channelId } = useParams();
  const [channel, setChannel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadChannelData();
  }, [channelId]);

  const loadChannelData = async () => {
    try {
      setLoading(true);
      const data = await api.getChannel(channelId);
      setChannel(data.channel);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading channel details...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!channel) return <div>Channel not found</div>;

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Channel Details</h1>
      <div style={{ background: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '2rem' }}>
          <img 
            src={channel.thumbnailUrl} 
            alt={channel.channelName}
            style={{ width: '100px', height: '100px', borderRadius: '50%' }}
          />
          <div>
            <h2>{channel.channelName}</h2>
            <p>{api.formatNumber(channel.subscriberCount)} subscribers</p>
            <p>{channel.analytics?.totalVideos || 0} videos</p>
            <p>{api.formatNumber(channel.analytics?.totalViews || 0)} total views</p>
          </div>
        </div>
        
        <div style={{ marginBottom: '2rem' }}>
          <h3>Description</h3>
          <p style={{ color: '#666', lineHeight: '1.6' }}>
            {channel.description || 'No description available'}
          </p>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div style={{ background: '#f8fafc', padding: '1rem', borderRadius: '8px' }}>
            <h4>Total Videos</h4>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#667eea' }}>
              {channel.analytics?.totalVideos || 0}
            </p>
          </div>
          <div style={{ background: '#f8fafc', padding: '1rem', borderRadius: '8px' }}>
            <h4>Average Views</h4>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#667eea' }}>
              {api.formatNumber(channel.analytics?.averageViews || 0)}
            </p>
          </div>
          <div style={{ background: '#f8fafc', padding: '1rem', borderRadius: '8px' }}>
            <h4>Upload Frequency</h4>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#667eea' }}>
              {channel.analytics?.uploadFrequency ? 
                `${channel.analytics.uploadFrequency.toFixed(1)}/month` : 
                'N/A'
              }
            </p>
          </div>
          <div style={{ background: '#f8fafc', padding: '1rem', borderRadius: '8px' }}>
            <h4>Average Duration</h4>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#667eea' }}>
              {channel.analytics?.averageDuration ? 
                api.formatDuration(channel.analytics.averageDuration) : 
                'N/A'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChannelDetails;
