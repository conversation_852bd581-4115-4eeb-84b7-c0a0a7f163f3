import { useState, useEffect } from 'react';
import api from '../services/api';

function QueueStatus() {
  const [queueStats, setQueueStats] = useState(null);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadQueueData();
    const interval = setInterval(loadQueueData, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadQueueData = async () => {
    try {
      const [statsData, jobsData] = await Promise.all([
        api.getQueueStats(),
        api.getQueueJobs(1, 20)
      ]);
      setQueueStats(statsData);
      setJobs(jobsData.jobs);
    } catch (err) {
      console.error('Failed to load queue data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRetryJob = async (jobId) => {
    try {
      await api.retryJob(jobId);
      loadQueueData(); // Refresh data
    } catch (err) {
      console.error('Failed to retry job:', err);
    }
  };

  if (loading) return <div>Loading queue status...</div>;

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>Queue Status</h1>
      
      {queueStats && (
        <div style={{ background: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '2rem' }}>
          <h2>Queue Statistics</h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
            {Object.entries(queueStats.queueStats).map(([queueName, stats]) => (
              <div key={queueName} style={{ 
                border: '1px solid #e5e7eb', 
                padding: '1rem', 
                borderRadius: '8px',
                background: '#f8fafc'
              }}>
                <h3 style={{ margin: '0 0 1rem 0' }}>
                  {queueName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '0.5rem', fontSize: '0.9rem' }}>
                  <div>Waiting: <strong>{stats.waiting}</strong></div>
                  <div>Active: <strong>{stats.active}</strong></div>
                  <div>Completed: <strong>{stats.completed}</strong></div>
                  <div>Failed: <strong>{stats.failed}</strong></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div style={{ background: 'white', padding: '2rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h2>Recent Jobs</h2>
        {jobs.length === 0 ? (
          <p>No jobs found</p>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '2px solid #e5e7eb' }}>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Job ID</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Type</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Status</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Channel</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Created</th>
                  <th style={{ padding: '0.75rem', textAlign: 'left' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobs.map((job) => (
                  <tr key={job.jobId} style={{ borderBottom: '1px solid #e5e7eb' }}>
                    <td style={{ padding: '0.75rem', fontSize: '0.8rem', fontFamily: 'monospace' }}>
                      {job.jobId.substring(0, 20)}...
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      {job.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.8rem',
                        fontWeight: 'bold',
                        background: 
                          job.status === 'completed' ? '#d1fae5' :
                          job.status === 'failed' ? '#fee2e2' :
                          job.status === 'processing' ? '#dbeafe' :
                          '#fef3c7',
                        color:
                          job.status === 'completed' ? '#065f46' :
                          job.status === 'failed' ? '#991b1b' :
                          job.status === 'processing' ? '#1e40af' :
                          '#92400e'
                      }}>
                        {job.status.toUpperCase()}
                      </span>
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      {job.data?.channelName || job.data?.channelId || 'N/A'}
                    </td>
                    <td style={{ padding: '0.75rem', fontSize: '0.9rem' }}>
                      {api.formatDateTime(job.createdAt)}
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      {job.status === 'failed' && (
                        <button
                          onClick={() => handleRetryJob(job.jobId)}
                          style={{
                            padding: '0.25rem 0.5rem',
                            background: '#667eea',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            fontSize: '0.8rem',
                            cursor: 'pointer'
                          }}
                        >
                          Retry
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

export default QueueStatus;
