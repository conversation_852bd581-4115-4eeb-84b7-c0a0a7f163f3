import mongoose from 'mongoose';

const channelSchema = new mongoose.Schema({
  channelId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  channelName: {
    type: String,
    required: true
  },
  channelHandle: {
    type: String,
    sparse: true
  },
  description: String,
  subscriberCount: {
    type: Number,
    default: 0
  },
  videoCount: {
    type: Number,
    default: 0
  },
  viewCount: {
    type: Number,
    default: 0
  },
  thumbnailUrl: String,
  publishedAt: Date,
  country: String,
  customUrl: String,
  
  // Metadata
  lastFetched: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  fetchStatus: {
    type: String,
    enum: ['pending', 'fetching', 'completed', 'error'],
    default: 'pending'
  },
  errorMessage: String,
  
  // Analytics data
  analytics: {
    totalVideos: { type: Number, default: 0 },
    totalViews: { type: Number, default: 0 },
    totalLikes: { type: Number, default: 0 },
    averageViews: { type: Number, default: 0 },
    averageDuration: { type: Number, default: 0 },
    uploadFrequency: { type: Number, default: 0 }, // videos per month
    lastVideoDate: Date,
    firstVideoDate: Date
  }
}, {
  timestamps: true
});

// Indexes for better query performance
channelSchema.index({ channelName: 'text', description: 'text' });
channelSchema.index({ lastFetched: 1 });
channelSchema.index({ fetchStatus: 1 });

export const Channel = mongoose.model('Channel', channelSchema);
