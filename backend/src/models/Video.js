import mongoose from 'mongoose';

const videoSchema = new mongoose.Schema({
  videoId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  channelId: {
    type: String,
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true
  },
  description: String,
  publishedAt: {
    type: Date,
    required: true,
    index: true
  },
  duration: {
    type: Number, // in seconds
    required: true
  },
  
  // Statistics
  viewCount: {
    type: Number,
    default: 0
  },
  likeCount: {
    type: Number,
    default: 0
  },
  commentCount: {
    type: Number,
    default: 0
  },
  
  // Metadata
  thumbnailUrl: String,
  tags: [String],
  categoryId: String,
  defaultLanguage: String,
  defaultAudioLanguage: String,
  
  // Video details
  definition: {
    type: String,
    enum: ['hd', 'sd'],
    default: 'hd'
  },
  caption: {
    type: Boolean,
    default: false
  },
  licensedContent: {
    type: Boolean,
    default: false
  },
  
  // Analytics metadata
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  
  // Derived analytics
  analytics: {
    engagementRate: Number, // (likes + comments) / views
    viewsPerDay: Number, // views / days since published
    peakViewsPeriod: String, // when most views occurred
    hashtagCount: Number,
    titleLength: Number,
    descriptionLength: Number
  }
}, {
  timestamps: true
});

// Compound indexes for efficient queries
videoSchema.index({ channelId: 1, publishedAt: -1 });
videoSchema.index({ publishedAt: -1, viewCount: -1 });
videoSchema.index({ tags: 1 });
videoSchema.index({ title: 'text', description: 'text', tags: 'text' });

// Virtual for calculating engagement rate
videoSchema.virtual('engagementRate').get(function() {
  if (this.viewCount === 0) return 0;
  return ((this.likeCount + this.commentCount) / this.viewCount) * 100;
});

export const Video = mongoose.model('Video', videoSchema);
