import mongoose from 'mongoose';

const queueJobSchema = new mongoose.Schema({
  jobId: {
    type: String,
    required: true,
    unique: true
  },
  type: {
    type: String,
    required: true,
    enum: ['channel_fetch', 'video_fetch', 'analytics_update', 'channel_search']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'processing', 'completed', 'failed', 'retrying'],
    default: 'pending'
  },
  priority: {
    type: Number,
    default: 0 // Higher number = higher priority
  },
  
  // Job data
  data: {
    channelId: String,
    channelName: String,
    videoIds: [String],
    searchQuery: String,
    options: mongoose.Schema.Types.Mixed
  },
  
  // Execution details
  attempts: {
    type: Number,
    default: 0
  },
  maxAttempts: {
    type: Number,
    default: 3
  },
  lastAttempt: Date,
  nextAttempt: Date,
  
  // Results
  result: mongoose.Schema.Types.Mixed,
  error: {
    message: String,
    stack: String,
    code: String
  },
  
  // Timing
  startedAt: Date,
  completedAt: Date,
  duration: Number, // in milliseconds
  
  // API usage tracking
  apiCallsUsed: {
    type: Number,
    default: 0
  },
  quotaUsed: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for efficient queue processing
queueJobSchema.index({ status: 1, priority: -1, createdAt: 1 });
queueJobSchema.index({ type: 1, status: 1 });
queueJobSchema.index({ nextAttempt: 1 });
queueJobSchema.index({ 'data.channelId': 1 });

// Methods
queueJobSchema.methods.markAsProcessing = function() {
  this.status = 'processing';
  this.startedAt = new Date();
  this.lastAttempt = new Date();
  this.attempts += 1;
  return this.save();
};

queueJobSchema.methods.markAsCompleted = function(result) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.duration = this.completedAt - this.startedAt;
  this.result = result;
  return this.save();
};

queueJobSchema.methods.markAsFailed = function(error) {
  this.status = 'failed';
  this.completedAt = new Date();
  this.error = {
    message: error.message,
    stack: error.stack,
    code: error.code
  };
  return this.save();
};

queueJobSchema.methods.scheduleRetry = function(delayMinutes = 5) {
  if (this.attempts < this.maxAttempts) {
    this.status = 'retrying';
    this.nextAttempt = new Date(Date.now() + delayMinutes * 60 * 1000);
    return this.save();
  } else {
    return this.markAsFailed(new Error('Max attempts exceeded'));
  }
};

export const QueueJob = mongoose.model('QueueJob', queueJobSchema);
