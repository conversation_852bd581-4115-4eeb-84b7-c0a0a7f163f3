import mongoose from 'mongoose';

const channelGroupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  description: String,
  channels: [{
    channelId: {
      type: String,
      required: true
    },
    channelName: String,
    addedAt: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  
  // Group metadata
  createdBy: String, // Could be user ID in future
  isPublic: {
    type: Boolean,
    default: true
  },
  tags: [String],
  
  // Aggregated analytics
  analytics: {
    totalChannels: { type: Number, default: 0 },
    totalVideos: { type: Number, default: 0 },
    totalViews: { type: Number, default: 0 },
    totalSubscribers: { type: Number, default: 0 },
    averageUploadFrequency: { type: Number, default: 0 },
    lastCalculated: Date
  }
}, {
  timestamps: true
});

// Indexes
channelGroupSchema.index({ name: 'text', description: 'text' });
channelGroupSchema.index({ 'channels.channelId': 1 });
channelGroupSchema.index({ tags: 1 });

// Methods
channelGroupSchema.methods.addChannel = function(channelData) {
  const existingChannel = this.channels.find(ch => ch.channelId === channelData.channelId);
  if (!existingChannel) {
    this.channels.push(channelData);
    this.analytics.totalChannels = this.channels.filter(ch => ch.isActive).length;
  }
  return this;
};

channelGroupSchema.methods.removeChannel = function(channelId) {
  this.channels = this.channels.filter(ch => ch.channelId !== channelId);
  this.analytics.totalChannels = this.channels.filter(ch => ch.isActive).length;
  return this;
};

channelGroupSchema.methods.getActiveChannels = function() {
  return this.channels.filter(ch => ch.isActive);
};

export const ChannelGroup = mongoose.model('ChannelGroup', channelGroupSchema);
