import { Channel } from '../models/Channel.js';
import { Video } from '../models/Video.js';
import { YouTubeAPI } from './YouTubeAPI.js';

export class ChannelService {
  constructor() {
    this.youtubeAPI = new YouTubeAPI();
  }

  async searchChannels(query) {
    try {
      const searchResults = await this.youtubeAPI.searchChannels(query, 10);
      
      const channels = searchResults.items.map(item => ({
        channelId: item.id.channelId,
        channelName: item.snippet.title,
        description: item.snippet.description,
        thumbnailUrl: item.snippet.thumbnails.default?.url,
        publishedAt: new Date(item.snippet.publishedAt)
      }));

      return channels;
    } catch (error) {
      throw new Error(`Channel search failed: ${error.message}`);
    }
  }

  async fetchAndStoreChannel(channelId) {
    try {
      // Check if channel already exists
      let channel = await Channel.findOne({ channelId });
      
      // Fetch channel details from YouTube API
      const channelData = await this.youtubeAPI.getChannelDetails(channelId);
      
      if (!channelData.items || channelData.items.length === 0) {
        throw new Error('Channel not found');
      }

      const ytChannel = channelData.items[0];
      const snippet = ytChannel.snippet;
      const statistics = ytChannel.statistics;

      const channelInfo = {
        channelId,
        channelName: snippet.title,
        channelHandle: snippet.customUrl,
        description: snippet.description,
        subscriberCount: parseInt(statistics.subscriberCount || 0),
        videoCount: parseInt(statistics.videoCount || 0),
        viewCount: parseInt(statistics.viewCount || 0),
        thumbnailUrl: snippet.thumbnails.default?.url,
        publishedAt: new Date(snippet.publishedAt),
        country: snippet.country,
        customUrl: snippet.customUrl,
        lastFetched: new Date(),
        fetchStatus: 'completed'
      };

      if (channel) {
        // Update existing channel
        Object.assign(channel, channelInfo);
        await channel.save();
      } else {
        // Create new channel
        channel = new Channel(channelInfo);
        await channel.save();
      }

      return channel;
    } catch (error) {
      // Update channel status to error if it exists
      const channel = await Channel.findOne({ channelId });
      if (channel) {
        channel.fetchStatus = 'error';
        channel.errorMessage = error.message;
        await channel.save();
      }
      throw error;
    }
  }

  async updateChannelAnalytics(channelId) {
    try {
      const channel = await Channel.findOne({ channelId });
      if (!channel) {
        throw new Error('Channel not found');
      }

      // Get all videos for this channel
      const videos = await Video.find({ channelId }).sort({ publishedAt: 1 });
      
      if (videos.length === 0) {
        return channel;
      }

      // Calculate analytics
      const analytics = {
        totalVideos: videos.length,
        totalViews: videos.reduce((sum, video) => sum + video.viewCount, 0),
        totalLikes: videos.reduce((sum, video) => sum + video.likeCount, 0),
        averageViews: 0,
        averageDuration: 0,
        uploadFrequency: 0,
        firstVideoDate: videos[0].publishedAt,
        lastVideoDate: videos[videos.length - 1].publishedAt
      };

      analytics.averageViews = Math.round(analytics.totalViews / analytics.totalVideos);
      analytics.averageDuration = Math.round(
        videos.reduce((sum, video) => sum + video.duration, 0) / analytics.totalVideos
      );

      // Calculate upload frequency (videos per month)
      const daysBetween = (analytics.lastVideoDate - analytics.firstVideoDate) / (1000 * 60 * 60 * 24);
      const monthsBetween = daysBetween / 30.44; // Average days per month
      analytics.uploadFrequency = monthsBetween > 0 ? analytics.totalVideos / monthsBetween : 0;

      // Update channel with analytics
      channel.analytics = analytics;
      await channel.save();

      return channel;
    } catch (error) {
      throw new Error(`Analytics update failed: ${error.message}`);
    }
  }

  async getChannelById(channelId) {
    return await Channel.findOne({ channelId });
  }

  async getAllChannels(page = 1, limit = 20, sortBy = 'lastFetched', sortOrder = 'desc') {
    const skip = (page - 1) * limit;
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const channels = await Channel.find({})
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Channel.countDocuments({});

    return {
      channels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getChannelAnalytics(channelId, timeframe = 'all') {
    const channel = await Channel.findOne({ channelId });
    if (!channel) {
      throw new Error('Channel not found');
    }

    // Build date filter based on timeframe
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case 'week':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case 'month':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
      case 'year':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) };
        break;
      default:
        // 'all' - no date filter
        break;
    }

    const videos = await Video.find({ channelId, ...dateFilter }).sort({ publishedAt: 1 });

    return {
      channel,
      videos,
      analytics: channel.analytics,
      timeframe
    };
  }

  async deleteChannel(channelId) {
    // Delete all videos for this channel
    await Video.deleteMany({ channelId });
    
    // Delete the channel
    const result = await Channel.deleteOne({ channelId });
    
    return result.deletedCount > 0;
  }

  async refreshChannelData(channelId) {
    try {
      // Mark channel as fetching
      await Channel.updateOne(
        { channelId },
        { fetchStatus: 'fetching', lastFetched: new Date() }
      );

      // Fetch fresh data
      const channel = await this.fetchAndStoreChannel(channelId);
      
      return channel;
    } catch (error) {
      throw new Error(`Channel refresh failed: ${error.message}`);
    }
  }
}
