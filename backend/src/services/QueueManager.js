import <PERSON> from 'bull';
import { QueueJob } from '../models/QueueJob.js';
import { ChannelService } from './ChannelService.js';
import { VideoService } from './VideoService.js';

export class QueueManager {
  static instance = null;
  
  constructor() {
    this.redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    this.queues = {};
    this.services = {};
  }

  static async initialize() {
    if (!this.instance) {
      this.instance = new QueueManager();
      await this.instance.setup();
    }
    return this.instance;
  }

  static getInstance() {
    return this.instance;
  }

  async setup() {
    // Initialize services
    this.services.channelService = new ChannelService();
    this.services.videoService = new VideoService();

    // Create queues with different priorities
    this.queues.channelFetch = new Bull('channel-fetch', this.redisUrl, {
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.queues.videoFetch = new Bull('video-fetch', this.redisUrl, {
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.queues.analyticsUpdate = new Bull('analytics-update', this.redisUrl, {
      defaultJobOptions: {
        removeOnComplete: 5,
        removeOnFail: 3,
        attempts: 2,
      },
    });

    // Set up processors
    this.setupProcessors();
    
    // Set up queue events
    this.setupQueueEvents();

    console.log('✅ Queue Manager initialized with Redis');
  }

  setupProcessors() {
    // Channel fetch processor
    this.queues.channelFetch.process(async (job) => {
      const { channelId, channelName } = job.data;
      console.log(`Processing channel fetch: ${channelName || channelId}`);
      
      try {
        const result = await this.services.channelService.fetchAndStoreChannel(channelId);
        await this.updateJobStatus(job.id, 'completed', result);
        return result;
      } catch (error) {
        await this.updateJobStatus(job.id, 'failed', null, error);
        throw error;
      }
    });

    // Video fetch processor
    this.queues.videoFetch.process(async (job) => {
      const { channelId, limit } = job.data;
      console.log(`Processing video fetch for channel: ${channelId}`);
      
      try {
        const result = await this.services.videoService.fetchAndStoreVideos(channelId, limit);
        await this.updateJobStatus(job.id, 'completed', result);
        return result;
      } catch (error) {
        await this.updateJobStatus(job.id, 'failed', null, error);
        throw error;
      }
    });

    // Analytics update processor
    this.queues.analyticsUpdate.process(async (job) => {
      const { channelId } = job.data;
      console.log(`Processing analytics update for channel: ${channelId}`);
      
      try {
        const result = await this.services.channelService.updateChannelAnalytics(channelId);
        await this.updateJobStatus(job.id, 'completed', result);
        return result;
      } catch (error) {
        await this.updateJobStatus(job.id, 'failed', null, error);
        throw error;
      }
    });
  }

  setupQueueEvents() {
    Object.values(this.queues).forEach(queue => {
      queue.on('completed', (job, result) => {
        console.log(`✅ Job ${job.id} completed`);
      });

      queue.on('failed', (job, err) => {
        console.log(`❌ Job ${job.id} failed: ${err.message}`);
      });

      queue.on('stalled', (job) => {
        console.log(`⚠️ Job ${job.id} stalled`);
      });
    });
  }

  async addChannelFetchJob(channelId, channelName = null, priority = 0) {
    const jobData = {
      channelId,
      channelName,
      priority
    };

    // Create database record
    const queueJob = new QueueJob({
      jobId: `channel_fetch_${channelId}_${Date.now()}`,
      type: 'channel_fetch',
      data: jobData,
      priority
    });
    await queueJob.save();

    // Add to Bull queue
    const job = await this.queues.channelFetch.add(jobData, {
      priority,
      jobId: queueJob.jobId
    });

    return { queueJob, bullJob: job };
  }

  async addVideoFetchJob(channelId, limit = null, priority = 0) {
    const jobData = {
      channelId,
      limit,
      priority
    };

    const queueJob = new QueueJob({
      jobId: `video_fetch_${channelId}_${Date.now()}`,
      type: 'video_fetch',
      data: jobData,
      priority
    });
    await queueJob.save();

    const job = await this.queues.videoFetch.add(jobData, {
      priority,
      jobId: queueJob.jobId
    });

    return { queueJob, bullJob: job };
  }

  async addAnalyticsUpdateJob(channelId, priority = 0) {
    const jobData = {
      channelId,
      priority
    };

    const queueJob = new QueueJob({
      jobId: `analytics_update_${channelId}_${Date.now()}`,
      type: 'analytics_update',
      data: jobData,
      priority
    });
    await queueJob.save();

    const job = await this.queues.analyticsUpdate.add(jobData, {
      priority,
      jobId: queueJob.jobId
    });

    return { queueJob, bullJob: job };
  }

  async updateJobStatus(jobId, status, result = null, error = null) {
    try {
      const queueJob = await QueueJob.findOne({ jobId });
      if (queueJob) {
        queueJob.status = status;
        if (result) queueJob.result = result;
        if (error) {
          queueJob.error = {
            message: error.message,
            stack: error.stack
          };
        }
        await queueJob.save();
      }
    } catch (err) {
      console.error('Failed to update job status:', err);
    }
  }

  async getQueueStats() {
    const stats = {};
    
    for (const [name, queue] of Object.entries(this.queues)) {
      const waiting = await queue.getWaiting();
      const active = await queue.getActive();
      const completed = await queue.getCompleted();
      const failed = await queue.getFailed();
      
      stats[name] = {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length
      };
    }
    
    return stats;
  }

  async pauseQueue(queueName) {
    if (this.queues[queueName]) {
      await this.queues[queueName].pause();
    }
  }

  async resumeQueue(queueName) {
    if (this.queues[queueName]) {
      await this.queues[queueName].resume();
    }
  }
}
