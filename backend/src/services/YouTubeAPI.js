import axios from 'axios';

export class YouTubeAPI {
  constructor() {
    this.apiKey = process.env.YOUTUBE_API_KEY;
    this.baseURL = 'https://www.googleapis.com/youtube/v3';
    this.quotaUsed = 0;
    this.dailyQuotaLimit = 10000;
  }

  async searchChannels(query, maxResults = 10) {
    const url = `${this.baseURL}/search`;
    const params = {
      part: 'snippet',
      type: 'channel',
      q: query,
      maxResults,
      key: this.apiKey
    };

    try {
      const response = await axios.get(url, { params });
      this.quotaUsed += 100; // Search costs 100 units
      return response.data;
    } catch (error) {
      throw new Error(`YouTube API search error: ${error.message}`);
    }
  }

  async getChannelDetails(channelId) {
    const url = `${this.baseURL}/channels`;
    const params = {
      part: 'snippet,statistics,contentDetails',
      id: channelId,
      key: this.apiKey
    };

    try {
      const response = await axios.get(url, { params });
      this.quotaUsed += 1; // Channel details cost 1 unit
      return response.data;
    } catch (error) {
      throw new Error(`YouTube API channel details error: ${error.message}`);
    }
  }

  async getChannelVideos(channelId, maxResults = 50, pageToken = null) {
    // First get the uploads playlist ID
    const channelData = await this.getChannelDetails(channelId);
    const uploadsPlaylistId = channelData.items[0]?.contentDetails?.relatedPlaylists?.uploads;
    
    if (!uploadsPlaylistId) {
      throw new Error('Could not find uploads playlist for channel');
    }

    const url = `${this.baseURL}/playlistItems`;
    const params = {
      part: 'snippet,contentDetails',
      playlistId: uploadsPlaylistId,
      maxResults,
      key: this.apiKey
    };

    if (pageToken) {
      params.pageToken = pageToken;
    }

    try {
      const response = await axios.get(url, { params });
      this.quotaUsed += 1; // Playlist items cost 1 unit
      return response.data;
    } catch (error) {
      throw new Error(`YouTube API playlist items error: ${error.message}`);
    }
  }

  async getVideoDetails(videoIds) {
    if (!Array.isArray(videoIds)) {
      videoIds = [videoIds];
    }

    const url = `${this.baseURL}/videos`;
    const params = {
      part: 'snippet,statistics,contentDetails',
      id: videoIds.join(','),
      key: this.apiKey
    };

    try {
      const response = await axios.get(url, { params });
      this.quotaUsed += 1; // Video details cost 1 unit
      return response.data;
    } catch (error) {
      throw new Error(`YouTube API video details error: ${error.message}`);
    }
  }

  parseDuration(duration) {
    // Parse ISO 8601 duration (PT4M13S) to seconds
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;
    
    const hours = parseInt(match[1] || 0);
    const minutes = parseInt(match[2] || 0);
    const seconds = parseInt(match[3] || 0);
    
    return hours * 3600 + minutes * 60 + seconds;
  }

  async getAllChannelVideos(channelId, limit = null) {
    const videos = [];
    let pageToken = null;
    let totalFetched = 0;

    do {
      const response = await this.getChannelVideos(channelId, 50, pageToken);
      const videoIds = response.items.map(item => item.contentDetails.videoId);
      
      // Get detailed video information
      const videoDetails = await this.getVideoDetails(videoIds);
      videos.push(...videoDetails.items);
      
      totalFetched += response.items.length;
      pageToken = response.nextPageToken;
      
      // Respect rate limits - add delay between requests
      await this.delay(100);
      
    } while (pageToken && (!limit || totalFetched < limit));

    return videos;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getRemainingQuota() {
    return this.dailyQuotaLimit - this.quotaUsed;
  }

  resetQuota() {
    this.quotaUsed = 0;
  }
}
