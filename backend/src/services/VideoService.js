import { Video } from '../models/Video.js';
import { Channel } from '../models/Channel.js';
import { YouTubeAPI } from './YouTubeAPI.js';

export class VideoService {
  constructor() {
    this.youtubeAPI = new YouTubeAPI();
  }

  async fetchAndStoreVideos(channelId, limit = null) {
    try {
      // Check if channel exists
      const channel = await Channel.findOne({ channelId });
      if (!channel) {
        throw new Error('Channel not found');
      }

      // Update channel status
      channel.fetchStatus = 'fetching';
      await channel.save();

      // Fetch videos from YouTube API
      const videos = await this.youtubeAPI.getAllChannelVideos(channelId, limit);
      
      const processedVideos = [];
      
      for (const ytVideo of videos) {
        try {
          const videoData = this.processVideoData(ytVideo, channelId);
          
          // Check if video already exists
          let video = await Video.findOne({ videoId: videoData.videoId });
          
          if (video) {
            // Update existing video
            Object.assign(video, videoData);
            video.lastUpdated = new Date();
          } else {
            // Create new video
            video = new Video(videoData);
          }
          
          // Calculate analytics
          video.analytics = this.calculateVideoAnalytics(video);
          
          await video.save();
          processedVideos.push(video);
          
        } catch (videoError) {
          console.error(`Error processing video ${ytVideo.id}:`, videoError);
          continue;
        }
      }

      // Update channel status
      channel.fetchStatus = 'completed';
      channel.lastFetched = new Date();
      await channel.save();

      return {
        channelId,
        videosProcessed: processedVideos.length,
        totalVideos: videos.length,
        videos: processedVideos
      };

    } catch (error) {
      // Update channel status to error
      const channel = await Channel.findOne({ channelId });
      if (channel) {
        channel.fetchStatus = 'error';
        channel.errorMessage = error.message;
        await channel.save();
      }
      throw error;
    }
  }

  processVideoData(ytVideo, channelId) {
    const snippet = ytVideo.snippet;
    const statistics = ytVideo.statistics || {};
    const contentDetails = ytVideo.contentDetails;

    return {
      videoId: ytVideo.id,
      channelId,
      title: snippet.title,
      description: snippet.description || '',
      publishedAt: new Date(snippet.publishedAt),
      duration: this.youtubeAPI.parseDuration(contentDetails.duration),
      viewCount: parseInt(statistics.viewCount || 0),
      likeCount: parseInt(statistics.likeCount || 0),
      commentCount: parseInt(statistics.commentCount || 0),
      thumbnailUrl: snippet.thumbnails?.default?.url,
      tags: snippet.tags || [],
      categoryId: snippet.categoryId,
      defaultLanguage: snippet.defaultLanguage,
      defaultAudioLanguage: snippet.defaultAudioLanguage,
      definition: contentDetails.definition || 'hd',
      caption: contentDetails.caption === 'true',
      licensedContent: contentDetails.licensedContent || false,
      lastUpdated: new Date()
    };
  }

  calculateVideoAnalytics(video) {
    const analytics = {};
    
    // Engagement rate: (likes + comments) / views * 100
    analytics.engagementRate = video.viewCount > 0 
      ? ((video.likeCount + video.commentCount) / video.viewCount) * 100 
      : 0;
    
    // Views per day since published
    const daysSincePublished = (new Date() - video.publishedAt) / (1000 * 60 * 60 * 24);
    analytics.viewsPerDay = daysSincePublished > 0 ? video.viewCount / daysSincePublished : 0;
    
    // Count hashtags in title and description
    const hashtagRegex = /#\w+/g;
    const titleHashtags = (video.title.match(hashtagRegex) || []).length;
    const descHashtags = (video.description.match(hashtagRegex) || []).length;
    analytics.hashtagCount = titleHashtags + descHashtags;
    
    // Text lengths
    analytics.titleLength = video.title.length;
    analytics.descriptionLength = video.description.length;
    
    return analytics;
  }

  async getVideosByChannel(channelId, page = 1, limit = 20, sortBy = 'publishedAt', sortOrder = 'desc') {
    const skip = (page - 1) * limit;
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const videos = await Video.find({ channelId })
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Video.countDocuments({ channelId });

    return {
      videos,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getVideoAnalytics(channelId, timeframe = 'all', groupBy = 'month') {
    // Build date filter
    let dateFilter = { channelId };
    const now = new Date();
    
    switch (timeframe) {
      case 'week':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case 'month':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
      case 'year':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) };
        break;
    }

    const videos = await Video.find(dateFilter).sort({ publishedAt: 1 });

    // Group videos by time period
    const groupedData = this.groupVideosByTime(videos, groupBy);
    
    // Calculate aggregated metrics
    const analytics = this.calculateAggregatedAnalytics(videos);

    return {
      channelId,
      timeframe,
      groupBy,
      groupedData,
      analytics,
      totalVideos: videos.length
    };
  }

  groupVideosByTime(videos, groupBy) {
    const grouped = {};
    
    videos.forEach(video => {
      let key;
      const date = video.publishedAt;
      
      switch (groupBy) {
        case 'day':
          key = date.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'year':
          key = date.getFullYear().toString();
          break;
        default:
          key = 'all';
      }
      
      if (!grouped[key]) {
        grouped[key] = {
          period: key,
          videos: [],
          totalViews: 0,
          totalLikes: 0,
          totalComments: 0,
          totalDuration: 0,
          averageViews: 0,
          averageDuration: 0,
          videoCount: 0
        };
      }
      
      grouped[key].videos.push(video);
      grouped[key].totalViews += video.viewCount;
      grouped[key].totalLikes += video.likeCount;
      grouped[key].totalComments += video.commentCount;
      grouped[key].totalDuration += video.duration;
      grouped[key].videoCount += 1;
    });
    
    // Calculate averages
    Object.values(grouped).forEach(group => {
      group.averageViews = group.videoCount > 0 ? Math.round(group.totalViews / group.videoCount) : 0;
      group.averageDuration = group.videoCount > 0 ? Math.round(group.totalDuration / group.videoCount) : 0;
    });
    
    return grouped;
  }

  calculateAggregatedAnalytics(videos) {
    if (videos.length === 0) {
      return {
        totalVideos: 0,
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        averageViews: 0,
        averageDuration: 0,
        averageEngagement: 0,
        topTags: [],
        durationDistribution: {}
      };
    }

    const analytics = {
      totalVideos: videos.length,
      totalViews: videos.reduce((sum, v) => sum + v.viewCount, 0),
      totalLikes: videos.reduce((sum, v) => sum + v.likeCount, 0),
      totalComments: videos.reduce((sum, v) => sum + v.commentCount, 0),
      totalDuration: videos.reduce((sum, v) => sum + v.duration, 0)
    };

    analytics.averageViews = Math.round(analytics.totalViews / analytics.totalVideos);
    analytics.averageDuration = Math.round(analytics.totalDuration / analytics.totalVideos);
    analytics.averageEngagement = analytics.totalViews > 0 
      ? ((analytics.totalLikes + analytics.totalComments) / analytics.totalViews) * 100 
      : 0;

    // Top tags analysis
    const tagCounts = {};
    videos.forEach(video => {
      video.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    
    analytics.topTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count }));

    // Duration distribution
    analytics.durationDistribution = this.calculateDurationDistribution(videos);

    return analytics;
  }

  calculateDurationDistribution(videos) {
    const distribution = {
      'short': 0,    // < 1 min
      'medium': 0,   // 1-10 min
      'long': 0,     // 10-30 min
      'veryLong': 0  // > 30 min
    };

    videos.forEach(video => {
      const minutes = video.duration / 60;
      if (minutes < 1) distribution.short++;
      else if (minutes < 10) distribution.medium++;
      else if (minutes < 30) distribution.long++;
      else distribution.veryLong++;
    });

    return distribution;
  }

  async updateVideoAnalytics(videoId) {
    const video = await Video.findOne({ videoId });
    if (!video) {
      throw new Error('Video not found');
    }

    video.analytics = this.calculateVideoAnalytics(video);
    await video.save();

    return video;
  }

  async deleteVideosByChannel(channelId) {
    const result = await Video.deleteMany({ channelId });
    return result.deletedCount;
  }
}
