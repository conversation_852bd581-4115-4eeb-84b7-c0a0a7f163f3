import express from 'express';
import { VideoService } from '../services/VideoService.js';
import { ChannelService } from '../services/ChannelService.js';
import { ChannelGroup } from '../models/ChannelGroup.js';
import { Video } from '../models/Video.js';

const router = express.Router();
const videoService = new VideoService();
const channelService = new ChannelService();

// Get video analytics for a channel
router.get('/videos/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { timeframe = 'all', groupBy = 'month' } = req.query;

    const analytics = await videoService.getVideoAnalytics(channelId, timeframe, groupBy);
    res.json(analytics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get videos for a channel with pagination
router.get('/videos/:channelId/list', async (req, res) => {
  try {
    const { channelId } = req.params;
    const {
      page = 1,
      limit = 20,
      sortBy = 'publishedAt',
      sortOrder = 'desc'
    } = req.query;

    const result = await videoService.getVideosByChannel(
      channelId,
      parseInt(page),
      parseInt(limit),
      sortBy,
      sortOrder
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Compare multiple channels
router.post('/compare', async (req, res) => {
  try {
    const { channelIds, timeframe = 'all' } = req.body;
    
    if (!Array.isArray(channelIds) || channelIds.length === 0) {
      return res.status(400).json({ error: 'channelIds array is required' });
    }

    const comparisons = [];
    
    for (const channelId of channelIds) {
      try {
        const analytics = await channelService.getChannelAnalytics(channelId, timeframe);
        comparisons.push(analytics);
      } catch (error) {
        console.error(`Error fetching analytics for channel ${channelId}:`, error);
        comparisons.push({
          channelId,
          error: error.message
        });
      }
    }

    res.json({
      timeframe,
      comparisons,
      summary: generateComparisonSummary(comparisons)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get trending videos across all channels
router.get('/trending', async (req, res) => {
  try {
    const { 
      timeframe = 'week',
      limit = 20,
      sortBy = 'viewCount'
    } = req.query;

    // Build date filter
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case 'day':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) };
        break;
      case 'week':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case 'month':
        dateFilter.publishedAt = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
    }

    const sort = {};
    sort[sortBy] = -1;

    const trendingVideos = await Video.find(dateFilter)
      .sort(sort)
      .limit(parseInt(limit))
      .populate('channelId', 'channelName thumbnailUrl');

    res.json({
      timeframe,
      videos: trendingVideos,
      count: trendingVideos.length
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get hashtag analytics
router.get('/hashtags', async (req, res) => {
  try {
    const { 
      channelId,
      timeframe = 'all',
      limit = 50
    } = req.query;

    // Build match filter
    let matchFilter = {};
    if (channelId) {
      matchFilter.channelId = channelId;
    }

    // Add date filter
    const now = new Date();
    switch (timeframe) {
      case 'week':
        matchFilter.publishedAt = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
        break;
      case 'month':
        matchFilter.publishedAt = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
        break;
      case 'year':
        matchFilter.publishedAt = { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) };
        break;
    }

    const hashtagStats = await Video.aggregate([
      { $match: matchFilter },
      { $unwind: '$tags' },
      {
        $group: {
          _id: '$tags',
          count: { $sum: 1 },
          totalViews: { $sum: '$viewCount' },
          totalLikes: { $sum: '$likeCount' },
          averageViews: { $avg: '$viewCount' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: parseInt(limit) },
      {
        $project: {
          tag: '$_id',
          count: 1,
          totalViews: 1,
          totalLikes: 1,
          averageViews: { $round: '$averageViews' },
          _id: 0
        }
      }
    ]);

    res.json({
      timeframe,
      channelId: channelId || 'all',
      hashtags: hashtagStats,
      count: hashtagStats.length
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get upload frequency analytics
router.get('/upload-frequency/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { groupBy = 'month' } = req.query;

    const videos = await Video.find({ channelId }).sort({ publishedAt: 1 });
    
    if (videos.length === 0) {
      return res.json({
        channelId,
        groupBy,
        data: [],
        summary: { totalVideos: 0, averageFrequency: 0 }
      });
    }

    const groupedData = videoService.groupVideosByTime(videos, groupBy);
    
    // Calculate frequency data
    const frequencyData = Object.values(groupedData).map(group => ({
      period: group.period,
      videoCount: group.videoCount,
      totalViews: group.totalViews,
      averageViews: group.averageViews
    }));

    const totalPeriods = frequencyData.length;
    const averageFrequency = totalPeriods > 0 ? videos.length / totalPeriods : 0;

    res.json({
      channelId,
      groupBy,
      data: frequencyData,
      summary: {
        totalVideos: videos.length,
        totalPeriods,
        averageFrequency: Math.round(averageFrequency * 100) / 100
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get duration analytics
router.get('/duration/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { timeframe = 'all' } = req.query;

    const analytics = await videoService.getVideoAnalytics(channelId, timeframe);
    
    const durationData = {
      channelId,
      timeframe,
      distribution: analytics.analytics.durationDistribution,
      averageDuration: analytics.analytics.averageDuration,
      totalDuration: analytics.analytics.totalDuration,
      durationTrend: calculateDurationTrend(analytics.groupedData)
    };

    res.json(durationData);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Helper function to generate comparison summary
function generateComparisonSummary(comparisons) {
  const validComparisons = comparisons.filter(c => !c.error);
  
  if (validComparisons.length === 0) {
    return { error: 'No valid channel data for comparison' };
  }

  const summary = {
    totalChannels: validComparisons.length,
    totalVideos: validComparisons.reduce((sum, c) => sum + (c.analytics?.totalVideos || 0), 0),
    totalViews: validComparisons.reduce((sum, c) => sum + (c.analytics?.totalViews || 0), 0),
    averageSubscribers: Math.round(
      validComparisons.reduce((sum, c) => sum + (c.channel?.subscriberCount || 0), 0) / validComparisons.length
    ),
    topPerformer: null,
    mostActive: null
  };

  // Find top performer by views
  summary.topPerformer = validComparisons.reduce((top, current) => {
    const currentViews = current.analytics?.totalViews || 0;
    const topViews = top.analytics?.totalViews || 0;
    return currentViews > topViews ? current : top;
  });

  // Find most active by video count
  summary.mostActive = validComparisons.reduce((active, current) => {
    const currentVideos = current.analytics?.totalVideos || 0;
    const activeVideos = active.analytics?.totalVideos || 0;
    return currentVideos > activeVideos ? current : active;
  });

  return summary;
}

// Helper function to calculate duration trend
function calculateDurationTrend(groupedData) {
  const periods = Object.keys(groupedData).sort();
  if (periods.length < 2) return 'insufficient_data';

  const firstPeriod = groupedData[periods[0]];
  const lastPeriod = groupedData[periods[periods.length - 1]];

  const firstAvg = firstPeriod.averageDuration;
  const lastAvg = lastPeriod.averageDuration;

  if (lastAvg > firstAvg * 1.1) return 'increasing';
  if (lastAvg < firstAvg * 0.9) return 'decreasing';
  return 'stable';
}

export default router;
