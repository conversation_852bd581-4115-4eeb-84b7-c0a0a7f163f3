import express from 'express';
import { ChannelService } from '../services/ChannelService.js';
import { QueueManager } from '../services/QueueManager.js';

const router = express.Router();
const channelService = new ChannelService();

// Search for channels
router.get('/search', async (req, res) => {
  try {
    const { q: query } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Query parameter is required' });
    }

    const channels = await channelService.searchChannels(query);
    res.json({ channels });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all channels with pagination
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = 'lastFetched',
      sortOrder = 'desc'
    } = req.query;

    const result = await channelService.getAllChannels(
      parseInt(page),
      parseInt(limit),
      sortBy,
      sortOrder
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get specific channel
router.get('/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const channel = await channelService.getChannelById(channelId);
    
    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    res.json({ channel });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add channel to queue for fetching
router.post('/fetch', async (req, res) => {
  try {
    const { channelId, channelName, priority = 0 } = req.body;
    
    if (!channelId) {
      return res.status(400).json({ error: 'channelId is required' });
    }

    const queueManager = QueueManager.getInstance();
    const result = await queueManager.addChannelFetchJob(channelId, channelName, priority);

    res.json({
      message: 'Channel fetch job added to queue',
      jobId: result.queueJob.jobId,
      status: 'queued'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Fetch channel videos
router.post('/:channelId/videos/fetch', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { limit = null, priority = 0 } = req.body;

    const queueManager = QueueManager.getInstance();
    const result = await queueManager.addVideoFetchJob(channelId, limit, priority);

    res.json({
      message: 'Video fetch job added to queue',
      jobId: result.queueJob.jobId,
      status: 'queued'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get channel analytics
router.get('/:channelId/analytics', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { timeframe = 'all' } = req.query;

    const analytics = await channelService.getChannelAnalytics(channelId, timeframe);
    res.json(analytics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update channel analytics
router.post('/:channelId/analytics/update', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { priority = 0 } = req.body;

    const queueManager = QueueManager.getInstance();
    const result = await queueManager.addAnalyticsUpdateJob(channelId, priority);

    res.json({
      message: 'Analytics update job added to queue',
      jobId: result.queueJob.jobId,
      status: 'queued'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Refresh channel data
router.post('/:channelId/refresh', async (req, res) => {
  try {
    const { channelId } = req.params;
    const channel = await channelService.refreshChannelData(channelId);
    
    res.json({
      message: 'Channel data refreshed',
      channel
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete channel
router.delete('/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const deleted = await channelService.deleteChannel(channelId);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    res.json({ message: 'Channel deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
