import express from 'express';
import { QueueManager } from '../services/QueueManager.js';
import { QueueJob } from '../models/QueueJob.js';

const router = express.Router();

// Get queue statistics
router.get('/stats', async (req, res) => {
  try {
    const queueManager = QueueManager.getInstance();
    const stats = await queueManager.getQueueStats();
    
    // Get database job statistics
    const dbStats = await QueueJob.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const dbStatsFormatted = {};
    dbStats.forEach(stat => {
      dbStatsFormatted[stat._id] = stat.count;
    });

    res.json({
      queueStats: stats,
      databaseStats: dbStatsFormatted,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get job history with pagination
router.get('/jobs', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      type,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Build filter
    const filter = {};
    if (status) filter.status = status;
    if (type) filter.type = type;

    const jobs = await QueueJob.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await QueueJob.countDocuments(filter);

    res.json({
      jobs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get specific job details
router.get('/jobs/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = await QueueJob.findOne({ jobId });
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.json({ job });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Retry a failed job
router.post('/jobs/:jobId/retry', async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = await QueueJob.findOne({ jobId });
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    if (job.status !== 'failed') {
      return res.status(400).json({ error: 'Only failed jobs can be retried' });
    }

    // Reset job status
    job.status = 'pending';
    job.attempts = 0;
    job.error = undefined;
    job.nextAttempt = new Date();
    await job.save();

    // Add back to appropriate queue
    const queueManager = QueueManager.getInstance();
    let result;

    switch (job.type) {
      case 'channel_fetch':
        result = await queueManager.addChannelFetchJob(
          job.data.channelId,
          job.data.channelName,
          job.priority
        );
        break;
      case 'video_fetch':
        result = await queueManager.addVideoFetchJob(
          job.data.channelId,
          job.data.limit,
          job.priority
        );
        break;
      case 'analytics_update':
        result = await queueManager.addAnalyticsUpdateJob(
          job.data.channelId,
          job.priority
        );
        break;
      default:
        return res.status(400).json({ error: 'Unknown job type' });
    }

    res.json({
      message: 'Job retried successfully',
      jobId: result.queueJob.jobId
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Pause a queue
router.post('/pause/:queueName', async (req, res) => {
  try {
    const { queueName } = req.params;
    const queueManager = QueueManager.getInstance();
    
    await queueManager.pauseQueue(queueName);
    
    res.json({
      message: `Queue ${queueName} paused successfully`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Resume a queue
router.post('/resume/:queueName', async (req, res) => {
  try {
    const { queueName } = req.params;
    const queueManager = QueueManager.getInstance();
    
    await queueManager.resumeQueue(queueName);
    
    res.json({
      message: `Queue ${queueName} resumed successfully`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Clear completed jobs
router.delete('/jobs/completed', async (req, res) => {
  try {
    const { olderThan = 7 } = req.query; // days
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(olderThan));

    const result = await QueueJob.deleteMany({
      status: 'completed',
      completedAt: { $lt: cutoffDate }
    });

    res.json({
      message: `Deleted ${result.deletedCount} completed jobs older than ${olderThan} days`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get queue health status
router.get('/health', async (req, res) => {
  try {
    const queueManager = QueueManager.getInstance();
    const stats = await queueManager.getQueueStats();
    
    // Check for stuck jobs (processing for more than 30 minutes)
    const stuckJobsThreshold = new Date(Date.now() - 30 * 60 * 1000);
    const stuckJobs = await QueueJob.countDocuments({
      status: 'processing',
      startedAt: { $lt: stuckJobsThreshold }
    });

    // Check for high failure rate
    const recentJobs = await QueueJob.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // last hour
    });
    
    const recentFailures = await QueueJob.countDocuments({
      status: 'failed',
      createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }
    });

    const failureRate = recentJobs > 0 ? (recentFailures / recentJobs) * 100 : 0;

    const health = {
      status: 'healthy',
      issues: [],
      stats: {
        stuckJobs,
        recentJobs,
        recentFailures,
        failureRate: Math.round(failureRate * 100) / 100
      },
      queues: stats
    };

    // Determine health status
    if (stuckJobs > 0) {
      health.status = 'warning';
      health.issues.push(`${stuckJobs} jobs appear to be stuck`);
    }

    if (failureRate > 50) {
      health.status = 'critical';
      health.issues.push(`High failure rate: ${failureRate}%`);
    } else if (failureRate > 20) {
      health.status = 'warning';
      health.issues.push(`Elevated failure rate: ${failureRate}%`);
    }

    res.json(health);
  } catch (error) {
    res.status(500).json({ 
      status: 'error',
      error: error.message 
    });
  }
});

export default router;
