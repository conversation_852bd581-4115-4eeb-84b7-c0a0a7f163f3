import mongoose from 'mongoose';

export class DatabaseConnection {
  static instance = null;

  static async connect() {
    if (this.instance) {
      return this.instance;
    }

    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/youtube-analytics';
      
      this.instance = await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });

      mongoose.connection.on('error', (error) => {
        console.error('MongoDB connection error:', error);
      });

      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
      });

      return this.instance;
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  static async disconnect() {
    if (this.instance) {
      await mongoose.disconnect();
      this.instance = null;
    }
  }
}
