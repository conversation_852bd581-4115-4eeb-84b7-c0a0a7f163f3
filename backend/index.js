import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { DatabaseConnection } from './src/config/database.js';
import { QueueManager } from './src/services/QueueManager.js';
import channelRoutes from './src/routes/channelRoutes.js';
import analyticsRoutes from './src/routes/analyticsRoutes.js';
import queueRoutes from './src/routes/queueRoutes.js';

dotenv.config();

class Server {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 5000;
    this.setupMiddleware();
    this.setupRoutes();
    this.initializeServices();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    this.app.use('/api/channels', channelRoutes);
    this.app.use('/api/analytics', analyticsRoutes);
    this.app.use('/api/queue', queueRoutes);
    
    this.app.get('/api/health', (req, res) => {
      res.json({ status: 'OK', timestamp: new Date().toISOString() });
    });
  }

  async initializeServices() {
    try {
      // Initialize database connection
      await DatabaseConnection.connect();
      console.log('✅ Database connected successfully');

      // Initialize queue manager
      await QueueManager.initialize();
      console.log('✅ Queue manager initialized');

    } catch (error) {
      console.error('❌ Failed to initialize services:', error);
      process.exit(1);
    }
  }

  start() {
    this.app.listen(this.port, () => {
      console.log(`🚀 Server running on port ${this.port}`);
    });
  }
}

const server = new Server();
server.start();
